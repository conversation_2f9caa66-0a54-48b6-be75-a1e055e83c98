/// SBE消息解码器
///
/// 提供统一的SBE消息解码接口，支持所有Binance SBE消息类型
use super::{
    book_ticker::{SbeBookTicker, parse_sbe_bookticker},
    depth_snapshot::{SbeDepthSnapshot, parse_sbe_depth_snapshot},
    depth_update::{SbeDepthUpdate, parse_sbe_depth_update},
    schema::{SbeHeader, SbeMessageType},
    trade::{SbeTrade, parse_sbe_trades},
};

/// SBE消息枚举，包含所有支持的消息类型
#[derive(Debug)]
pub enum SbeMessage<'a> {
    BookTicker(SbeBookTicker<'a>),
    Trade(SbeTrade<'a>),
    DepthSnapshot(SbeDepthSnapshot<'a>),
    DepthUpdate(SbeDepthUpdate<'a>),
}

/// SBE解码器
pub struct SbeDecoder;

impl SbeDecoder {
    /// 检测数据是否为SBE格式
    ///
    /// # Arguments
    /// * `data` - 待检测的数据
    ///
    /// # Returns
    /// * `true` - 数据符合SBE格式
    /// * `false` - 数据不符合SBE格式
    pub fn is_sbe_message(data: &[u8]) -> bool {
        if let Some(header) = SbeHeader::from_bytes(data) {
            header.is_valid()
        } else {
            false
        }
    }

    /// 解码SBE消息
    ///
    /// # Arguments
    /// * `data` - 完整的SBE消息数据（包含头部）
    ///
    /// # Returns
    /// * `Some(SbeMessage)` - 解码成功
    /// * `None` - 解码失败
    pub fn decode(data: &[u8]) -> Option<SbeMessage<'_>> {
        // 解析SBE头部
        let header = SbeHeader::from_bytes(data)?;

        // 验证头部有效性
        if !header.is_valid() {
            return None;
        }

        // 根据消息类型进行解码
        match header.message_type() {
            SbeMessageType::BookTicker
            | SbeMessageType::BestBidAsk
            | SbeMessageType::BinanceBestBidAsk => {
                parse_sbe_bookticker(data).map(SbeMessage::BookTicker)
            }

            SbeMessageType::Trade => parse_sbe_trades(data).map(SbeMessage::Trade),

            SbeMessageType::DepthSnapshot => {
                parse_sbe_depth_snapshot(data).map(SbeMessage::DepthSnapshot)
            }

            SbeMessageType::DepthUpdate => {
                parse_sbe_depth_update(data).map(SbeMessage::DepthUpdate)
            }

            _ => None,
        }
    }

    /// 批量解码SBE消息
    ///
    /// # Arguments
    /// * `data` - 包含多个SBE消息的数据
    ///
    /// # Returns
    /// * `Vec<SbeMessage>` - 解码成功的消息列表
    pub fn decode_batch(data: &[u8]) -> Vec<SbeMessage<'_>> {
        let mut messages = Vec::new();
        let mut offset = 0;

        while offset < data.len() {
            // 尝试解析头部
            if let Some(header) = SbeHeader::from_bytes(&data[offset..]) {
                if header.is_valid() {
                    // 对于有变长字段的消息，需要先解码来确定实际长度
                    let remaining_data = &data[offset..];

                    // 尝试解码以确定消息的实际长度
                    if let Some(message) = Self::decode(remaining_data) {
                        // 计算实际消息长度
                        let actual_length = match &message {
                            SbeMessage::BookTicker(_) => {
                                SbeHeader::SIZE + header.block_length as usize
                            }
                            SbeMessage::Trade(_) => {
                                // Trade消息有变长字段，需要计算实际长度
                                Self::calculate_trade_message_length(remaining_data)
                            }
                            SbeMessage::DepthSnapshot(_) => {
                                // DepthSnapshot消息有变长字段，需要计算实际长度
                                Self::calculate_depth_snapshot_message_length(remaining_data)
                            }
                            SbeMessage::DepthUpdate(_) => {
                                // DepthUpdate消息有变长字段，需要计算实际长度
                                Self::calculate_depth_update_message_length(remaining_data)
                            }
                        };

                        messages.push(message);
                        offset += actual_length;
                    } else {
                        // 解码失败，跳过一个字节继续尝试
                        offset += 1;
                    }
                } else {
                    // 头部无效，跳过一个字节继续尝试
                    offset += 1;
                }
            } else {
                // 无法解析头部，跳过一个字节继续尝试
                offset += 1;
            }
        }

        messages
    }

    /// 获取消息类型（不进行完整解码）
    ///
    /// # Arguments
    /// * `data` - SBE消息数据
    ///
    /// # Returns
    /// * `Some(SbeMessageType)` - 消息类型
    /// * `None` - 无法确定类型
    pub fn get_message_type(data: &[u8]) -> Option<SbeMessageType> {
        let header = SbeHeader::from_bytes(data)?;
        if header.is_valid() {
            Some(header.message_type())
        } else {
            None
        }
    }

    /// 验证SBE消息完整性
    ///
    /// # Arguments
    /// * `data` - SBE消息数据
    ///
    /// # Returns
    /// * `true` - 消息完整
    /// * `false` - 消息不完整或损坏
    pub fn validate_message(data: &[u8]) -> bool {
        if let Some(header) = SbeHeader::from_bytes(data) {
            if header.is_valid() {
                let expected_length = SbeHeader::SIZE + header.block_length as usize;
                data.len() >= expected_length
            } else {
                false
            }
        } else {
            false
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    fn create_test_bookticker_sbe() -> Vec<u8> {
        let mut data = Vec::new();

        // SBE头部
        data.extend_from_slice(&56u16.to_le_bytes()); // block_length
        data.extend_from_slice(&10001u16.to_le_bytes()); // template_id (BinanceBestBidAsk)
        data.extend_from_slice(&1u16.to_le_bytes()); // schema_id
        data.extend_from_slice(&1u16.to_le_bytes()); // version

        // 消息体 (56字节)
        data.extend_from_slice(&1234567890123456u64.to_le_bytes()); // eventTime
        data.extend_from_slice(&987654321u64.to_le_bytes()); // bookUpdateId
        data.push((-8i8) as u8); // priceExponent
        data.push((-8i8) as u8); // qtyExponent
        data.extend_from_slice(&5000000000000i64.to_le_bytes()); // bidPrice
        data.extend_from_slice(&100000000i64.to_le_bytes()); // bidQty
        data.extend_from_slice(&5000100000000i64.to_le_bytes()); // askPrice
        data.extend_from_slice(&200000000i64.to_le_bytes()); // askQty

        // 填充到56字节
        while data.len() < 8 + 56 {
            data.push(0);
        }

        // symbol: "BTCUSDT"
        data.push(7u8); // 长度
        data.extend_from_slice(b"BTCUSDT");

        data
    }

    #[test]
    fn test_sbe_detection() {
        let sbe_data = create_test_bookticker_sbe();
        assert!(SbeDecoder::is_sbe_message(&sbe_data));

        // 测试无效数据
        let invalid_data = vec![0u8; 10];
        assert!(!SbeDecoder::is_sbe_message(&invalid_data));
    }

    #[test]
    fn test_sbe_decode() {
        let sbe_data = create_test_bookticker_sbe();

        let message = SbeDecoder::decode(&sbe_data).unwrap();
        match message {
            SbeMessage::BookTicker(ticker) => {
                assert_eq!(ticker.symbol, "BTCUSDT");
                assert_eq!(ticker.bid_price, 50000.0);
                assert_eq!(ticker.ask_price, 50001.0);
            }
            _ => panic!("Expected BookTicker message"),
        }
    }

    #[test]
    fn test_get_message_type() {
        let sbe_data = create_test_bookticker_sbe();

        let msg_type = SbeDecoder::get_message_type(&sbe_data).unwrap();
        assert_eq!(msg_type, SbeMessageType::BinanceBestBidAsk);
    }

    #[test]
    fn test_validate_message() {
        let sbe_data = create_test_bookticker_sbe();
        assert!(SbeDecoder::validate_message(&sbe_data));

        // 测试不完整的消息
        let incomplete_data = &sbe_data[..10];
        assert!(!SbeDecoder::validate_message(incomplete_data));
    }

    #[test]
    fn test_batch_decode() {
        let sbe_data1 = create_test_bookticker_sbe();
        let sbe_data2 = create_test_bookticker_sbe();

        let mut batch_data = Vec::new();
        batch_data.extend_from_slice(&sbe_data1);
        batch_data.extend_from_slice(&sbe_data2);

        let messages = SbeDecoder::decode_batch(&batch_data);
        assert_eq!(messages.len(), 2);

        for message in messages {
            match message {
                SbeMessage::BookTicker(ticker) => {
                    assert_eq!(ticker.symbol, "BTCUSDT");
                }
                _ => panic!("Expected BookTicker message"),
            }
        }
    }
}
