/// SBE格式的Depth20 (DepthSnapshotStreamEvent) 解析
///
/// 基于Binance官方SBE schema实现
/// Stream Name: <symbol>@depth20
/// SBE Message Name: DepthSnapshotStreamEvent
/// Update Speed: 100ms

/// SBE格式的订单簿快照条目
#[derive(Debug, Clone)]
pub struct SbeDepthLevel {
    pub price: f64,
    pub qty: f64,
}

/// SBE格式的订单簿快照 (Depth20)
#[derive(Debug)]
pub struct SbeDepthSnapshot<'a> {
    pub symbol: &'a str,
    pub event_time: u64,
    pub last_update_id: u64,
    pub bids: Vec<SbeDepthLevel>,
    pub asks: Vec<SbeDepthLevel>,
}

/// Binance SBE DepthSnapshotStreamEvent 字段偏移量
mod depth_snapshot_offsets {
    pub const EVENT_TIME_OFFSET: usize = 0;
    pub const LAST_UPDATE_ID_OFFSET: usize = 8;
    pub const PRICE_EXPONENT_OFFSET: usize = 16;
    pub const QTY_EXPONENT_OFFSET: usize = 17;
    pub const BIDS_COUNT_OFFSET: usize = 18;
    pub const ASKS_COUNT_OFFSET: usize = 19;
    pub const LEVELS_DATA_OFFSET: usize = 20;

    // 每个价格级别的大小 (price + qty = 8 + 8 = 16 bytes)
    pub const LEVEL_SIZE: usize = 16;
    pub const PRICE_SIZE: usize = 8;
    pub const QTY_SIZE: usize = 8;
}

/// 将mantissa和exponent转换为f64
fn mantissa_exponent_to_f64(mantissa: i64, exponent: i8) -> f64 {
    if exponent >= 0 {
        mantissa as f64 * 10_f64.powi(exponent as i32)
    } else {
        mantissa as f64 / 10_f64.powi((-exponent) as i32)
    }
}

/// 解析SBE格式的Depth20消息
///
/// Binance SBE DepthSnapshotStreamEvent消息格式：
/// - eventTime: 8字节 utcTimestampUs (int64)
/// - lastUpdateId: 8字节 updateId (int64)
/// - priceExponent: 1字节 exponent8 (int8)
/// - qtyExponent: 1字节 exponent8 (int8)
/// - bidsCount: 1字节 count8 (uint8)
/// - asksCount: 1字节 count8 (uint8)
/// - bids: 变长数组 (每个条目16字节: price mantissa + qty mantissa)
/// - asks: 变长数组 (每个条目16字节: price mantissa + qty mantissa)
/// - symbol: 变长字符串 varString8 (1字节长度 + 数据)
///
/// # Arguments
/// * `data` - SBE消息体数据（不包含头部）
///
/// # Returns
/// * `Some(SbeDepthSnapshot)` - 解析成功
/// * `None` - 解析失败
pub fn parse_sbe_depth_snapshot(data: &[u8]) -> Option<SbeDepthSnapshot<'_>> {
    use depth_snapshot_offsets::*;

    // 检查数据长度是否足够包含SBE头部
    if data.len() < 8 {
        return None;
    }

    // 解析并验证SBE头部
    let header = crate::encoding::sbe::schema::SbeHeader::from_bytes(data)?;
    if !header.is_valid() {
        return None;
    }

    // 跳过SBE头部，直接处理消息体
    let data = &data[8..];

    // 检查最小数据长度
    if data.len() < LEVELS_DATA_OFFSET {
        return None;
    }

    // 解析固定字段
    let event_time = u64::from_le_bytes([
        data[EVENT_TIME_OFFSET],
        data[EVENT_TIME_OFFSET + 1],
        data[EVENT_TIME_OFFSET + 2],
        data[EVENT_TIME_OFFSET + 3],
        data[EVENT_TIME_OFFSET + 4],
        data[EVENT_TIME_OFFSET + 5],
        data[EVENT_TIME_OFFSET + 6],
        data[EVENT_TIME_OFFSET + 7],
    ]);

    let last_update_id = u64::from_le_bytes([
        data[LAST_UPDATE_ID_OFFSET],
        data[LAST_UPDATE_ID_OFFSET + 1],
        data[LAST_UPDATE_ID_OFFSET + 2],
        data[LAST_UPDATE_ID_OFFSET + 3],
        data[LAST_UPDATE_ID_OFFSET + 4],
        data[LAST_UPDATE_ID_OFFSET + 5],
        data[LAST_UPDATE_ID_OFFSET + 6],
        data[LAST_UPDATE_ID_OFFSET + 7],
    ]);

    let price_exponent = data[PRICE_EXPONENT_OFFSET] as i8;
    let qty_exponent = data[QTY_EXPONENT_OFFSET] as i8;
    let bids_count = data[BIDS_COUNT_OFFSET] as usize;
    let asks_count = data[ASKS_COUNT_OFFSET] as usize;

    // 计算所需的数据长度
    let total_levels = bids_count + asks_count;
    let levels_data_size = total_levels * LEVEL_SIZE;
    let min_required_size = LEVELS_DATA_OFFSET + levels_data_size;

    if data.len() < min_required_size {
        return None;
    }

    // 解析bids
    let mut bids = Vec::with_capacity(bids_count);
    let mut offset = LEVELS_DATA_OFFSET;

    for _ in 0..bids_count {
        if offset + LEVEL_SIZE > data.len() {
            return None;
        }

        let price_mantissa = i64::from_le_bytes([
            data[offset],
            data[offset + 1],
            data[offset + 2],
            data[offset + 3],
            data[offset + 4],
            data[offset + 5],
            data[offset + 6],
            data[offset + 7],
        ]);

        let qty_mantissa = i64::from_le_bytes([
            data[offset + PRICE_SIZE],
            data[offset + PRICE_SIZE + 1],
            data[offset + PRICE_SIZE + 2],
            data[offset + PRICE_SIZE + 3],
            data[offset + PRICE_SIZE + 4],
            data[offset + PRICE_SIZE + 5],
            data[offset + PRICE_SIZE + 6],
            data[offset + PRICE_SIZE + 7],
        ]);

        let price = mantissa_exponent_to_f64(price_mantissa, price_exponent);
        let qty = mantissa_exponent_to_f64(qty_mantissa, qty_exponent);

        bids.push(SbeDepthLevel { price, qty });
        offset += LEVEL_SIZE;
    }

    // 解析asks
    let mut asks = Vec::with_capacity(asks_count);

    for _ in 0..asks_count {
        if offset + LEVEL_SIZE > data.len() {
            return None;
        }

        let price_mantissa = i64::from_le_bytes([
            data[offset],
            data[offset + 1],
            data[offset + 2],
            data[offset + 3],
            data[offset + 4],
            data[offset + 5],
            data[offset + 6],
            data[offset + 7],
        ]);

        let qty_mantissa = i64::from_le_bytes([
            data[offset + PRICE_SIZE],
            data[offset + PRICE_SIZE + 1],
            data[offset + PRICE_SIZE + 2],
            data[offset + PRICE_SIZE + 3],
            data[offset + PRICE_SIZE + 4],
            data[offset + PRICE_SIZE + 5],
            data[offset + PRICE_SIZE + 6],
            data[offset + PRICE_SIZE + 7],
        ]);

        let price = mantissa_exponent_to_f64(price_mantissa, price_exponent);
        let qty = mantissa_exponent_to_f64(qty_mantissa, qty_exponent);

        asks.push(SbeDepthLevel { price, qty });
        offset += LEVEL_SIZE;
    }

    // 解析symbol字段
    let symbol = if offset < data.len() {
        let symbol_length = data[offset] as usize;
        if offset + 1 + symbol_length <= data.len() {
            match std::str::from_utf8(&data[offset + 1..offset + 1 + symbol_length]) {
                Ok(s) => s,
                Err(_) => "UNKNOWN",
            }
        } else {
            "UNKNOWN"
        }
    } else {
        "UNKNOWN"
    };

    Some(SbeDepthSnapshot {
        symbol,
        event_time,
        last_update_id,
        bids,
        asks,
    })
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_parse_sbe_depth_snapshot() {
        // 创建测试数据
        let mut data = Vec::new();

        // SBE头部 (8字节)
        data.extend_from_slice(&[0u8; 8]);

        // eventTime (8字节)
        data.extend_from_slice(&1234567890123456u64.to_le_bytes());

        // lastUpdateId (8字节)
        data.extend_from_slice(&987654321u64.to_le_bytes());

        // priceExponent (-8)
        data.push((-8i8) as u8);

        // qtyExponent (-8)
        data.push((-8i8) as u8);

        // bidsCount (1)
        data.push(1u8);

        // asksCount (1)
        data.push(1u8);

        // bid: price=5000000000000 (50000.0), qty=100000000 (1.0)
        data.extend_from_slice(&5000000000000i64.to_le_bytes());
        data.extend_from_slice(&100000000i64.to_le_bytes());

        // ask: price=5000100000000 (50001.0), qty=200000000 (2.0)
        data.extend_from_slice(&5000100000000i64.to_le_bytes());
        data.extend_from_slice(&200000000i64.to_le_bytes());

        // symbol: "BTCUSDT"
        data.push(7u8); // 长度
        data.extend_from_slice(b"BTCUSDT");

        let result = parse_sbe_depth_snapshot(&data).unwrap();

        assert_eq!(result.symbol, "BTCUSDT");
        assert_eq!(result.event_time, 1234567890123456);
        assert_eq!(result.last_update_id, 987654321);
        assert_eq!(result.bids.len(), 1);
        assert_eq!(result.asks.len(), 1);
        assert_eq!(result.bids[0].price, 50000.0);
        assert_eq!(result.bids[0].qty, 1.0);
        assert_eq!(result.asks[0].price, 50001.0);
        assert_eq!(result.asks[0].qty, 2.0);
    }
}
