//! Ultra‑low‑latency stack‑allocated Binance order book implementation.
//!
//! * Completely allocation‑free after start‑up: no Vec, HashMap or heap at runtime.
//! * Suitable for `#![no_std]` environments (you can disable the few debug assertions).
//! * O(log N) search, O(N) insert/delete with `ptr::copy` memcpy‑style shifts.
//! * Best for depths up to a few thousand levels (N <= 5000) which covers Binance full book.
//!
//! Adapted from Binance “How to manage a local order book correctly” steps — see developers.binance.com.
//!
//! Compile with: `cargo build --release` (requires Rust 1.77+ for const‑generics).

use core::cmp::Ordering;
use core::ptr;
use std::fmt::Display;

/// A single order book level (price, quantity).
#[derive(Clone, Co<PERSON>, Debug, Default)]
#[repr(C)]
pub struct Level {
    pub price: f64,
    pub qty: f64,
}

/// A stack‑allocated side (bid or ask).
///
/// * `N` : maximum number of levels to keep.
/// * `IS_BID` : `true` for bids (sorted DESC), `false` for asks (ASC).
#[derive(<PERSON><PERSON>, Co<PERSON>, Debug)]
pub struct Side<const N: usize, const IS_BID: bool> {
    levels: [Level; N],
    len: usize,
}

impl<const N: usize, const IS_BID: bool> Side<N, IS_BID> {
    /// Empty side.
    #[inline]
    pub const fn new() -> Self {
        Self {
            levels: [Level {
                price: 0.0,
                qty: 0.0,
            }; N],
            len: 0,
        }
    }

    /// Binary search for a price. Returns (found, index).
    #[inline(always)]
    fn find(&self, price: f64) -> (bool, usize) {
        let mut low = 0usize;
        let mut high = self.len;
        while low < high {
            let mid = (low + high) >> 1;
            let mid_price = unsafe { self.levels.get_unchecked(mid).price };
            let ord = if IS_BID {
                if price > mid_price {
                    Ordering::Less
                } else if price < mid_price {
                    Ordering::Greater
                } else {
                    Ordering::Equal
                }
            } else {
                if price < mid_price {
                    Ordering::Less
                } else if price > mid_price {
                    Ordering::Greater
                } else {
                    Ordering::Equal
                }
            };

            match ord {
                Ordering::Less => high = mid,
                Ordering::Greater => low = mid + 1,
                Ordering::Equal => return (true, mid),
            }
        }
        (false, low)
    }

    /// Insert/update level. `qty == 0` removes.
    #[inline]
    pub fn upsert(&mut self, price: f64, qty: f64) {
        let (found, idx) = self.find(price);

        if found {
            if qty == 0.0 {
                // delete
                unsafe {
                    let dst = self.levels.as_mut_ptr().add(idx);
                    let src = dst.add(1);
                    ptr::copy(src, dst, self.len - idx - 1);
                }
                self.len -= 1;
            } else {
                unsafe {
                    self.levels.get_unchecked_mut(idx).qty = qty;
                }
            }
        } else if qty != 0.0 {
            debug_assert!(self.len < N, "Side capacity exceeded");
            unsafe {
                let dst = self.levels.as_mut_ptr().add(idx + 1);
                let src = self.levels.as_ptr().add(idx);
                ptr::copy(src, dst, self.len - idx); // shift right
                let level = self.levels.get_unchecked_mut(idx);
                level.price = price;
                level.qty = qty;
            }
            self.len += 1;
        }
    }

    #[inline]
    pub fn iter(&self) -> impl Iterator<Item = &Level> {
        self.levels[..self.len].iter()
    }
}

/// Complete order book (bids + asks) with monotonic update sequence tracking.
#[derive(Clone, Copy)]
pub struct OrderBook<const N: usize> {
    pub last_update_id: u64,
    bids: Side<N, true>,
    asks: Side<N, false>,
}

impl<const N: usize> OrderBook<N> {
    /// Empty book.
    pub const fn new() -> Self {
        Self {
            last_update_id: 0,
            bids: Side::new(),
            asks: Side::new(),
        }
    }

    /// Apply REST snapshot.
    pub fn apply_snapshot(
        &mut self,
        last_update_id: u64,
        bids: &[(f64, f64)],
        asks: &[(f64, f64)],
    ) {
        self.last_update_id = last_update_id;
        self.bids.len = 0;
        self.asks.len = 0;

        for &(p, q) in bids.iter().take(N) {
            self.bids.upsert(p, q);
        }
        for &(p, q) in asks.iter().take(N) {
            self.asks.upsert(p, q);
        }
    }

    /// Apply diff update (after sequence check outside).
    pub fn apply_diff(&mut self, u: u64, bid_updates: &[(f64, f64)], ask_updates: &[(f64, f64)]) {
        debug_assert!(u > self.last_update_id);
        for &(p, q) in bid_updates {
            self.bids.upsert(p, q);
        }
        for &(p, q) in ask_updates {
            self.asks.upsert(p, q);
        }
        self.last_update_id = u;
    }

    #[inline]
    pub fn best_bid(&self) -> Option<&Level> {
        if self.bids.len > 0 {
            Some(&self.bids.levels[0])
        } else {
            None
        }
    }

    #[inline]
    pub fn best_ask(&self) -> Option<&Level> {
        if self.asks.len > 0 {
            Some(&self.asks.levels[0])
        } else {
            None
        }
    }
}

impl<const N: usize> Display for OrderBook<N> {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "Orderbook Snapshot: ").unwrap();
        write!(f, "Last Update ID: {}", self.last_update_id).unwrap();
        for bid in self.bids.iter() {
            write!(f, "    Bid: {}@{}, ", bid.price, bid.qty).unwrap();
        }
        for ask in self.asks.iter() {
            write!(f, "    Ask: {}@{}, ", ask.price, ask.qty).unwrap();
        }
        write!(f, "\n")
    }
}

/// Example usage (depth 1000).
#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn smoke() {
        const N: usize = 1000;
        let mut book: OrderBook<N> = OrderBook::new();
        // snapshot
        book.apply_snapshot(10, &[(100.0, 1.0)], &[(101.0, 1.2)]);
        assert_eq!(book.best_bid().unwrap().price, 100.0);

        // diff update
        book.apply_diff(11, &[(100.0, 2.0)], &[(101.0, 0.0)]);
        assert_eq!(book.best_bid().unwrap().qty, 2.0);
        assert!(book.best_ask().is_none());
    }
}
