use crate::{
    Currency, EdgeDirection, ORDER_FILTER_LOT_SIZE_INDEX, ORDER_FILTER_MIN_ORDER_QTY_INDEX,
    ORDER_FILTERS, ORDER_QUANTITIES, PREDEFINED_RINGS, TRADING_PAIR_RATES,
    TRADING_PAIR_TO_RING_INDEX, TradingPair,
    encoding::sbe::SbeTrade,
    engine::trading_pair::{EXPECT_PRICE, TRADING_PAIR_RATE_UPDATE_TIME},
};

// BNB 折扣率 (75% 折扣，即支付原价的 75%)
// const BNB_DISCOUNT: f64 = 0.75;

// fn print_ring(index: usize) {
//     let ring = PREDEFINED_RINGS[index];
//     for (pair, direction) in ring {
//         print!("{:?} {:?} -> ", pair, direction);
//     }
//     println!("");
// }

fn adjust_quantity(qty: f64, min_qty: f64, step: f64) -> Option<f64> {
    if qty < min_qty {
        return None;
    }
    let steps = (qty / step).floor();
    let adjusted = steps * step;
    if adjusted >= min_qty {
        Some(adjusted)
    } else {
        let min_multiple = (min_qty / step).ceil() * step;
        if min_multiple > qty {
            None
        } else {
            Some(min_multiple)
        }
    }
}

fn quote_to_base_quantity(quote: f64, pair: TradingPair) -> f64 {
    unsafe { quote * TRADING_PAIR_RATES[pair as usize][EdgeDirection::Forward as usize] }
}

fn base_to_quote_quantity(base: f64, pair: TradingPair) -> f64 {
    unsafe { base * TRADING_PAIR_RATES[pair as usize][EdgeDirection::Reverse as usize] }
}

pub struct ArbitrageEngine {}

impl ArbitrageEngine {
    pub fn new() -> Self {
        Self {}
    }

    pub fn update_rate<T: Into<TradingPair>>(pair: T, bid: f64, ask: f64, event_time: u64) {
        let pair = pair.into();
        unsafe {
            // 如果bbo更慢，应该丢弃
            if event_time <= TRADING_PAIR_RATE_UPDATE_TIME[pair as usize] {
                return;
            }
            TRADING_PAIR_RATES[pair as usize][EdgeDirection::Forward as usize] = 1.0 / ask;
            TRADING_PAIR_RATES[pair as usize][EdgeDirection::Reverse as usize] = bid;
            TRADING_PAIR_RATE_UPDATE_TIME[pair as usize] = event_time;
        }
    }

    pub fn update_rate_by_trades(trade: SbeTrade) {
        let pair: TradingPair = trade.symbol.into();
        let update_time = unsafe { TRADING_PAIR_RATE_UPDATE_TIME[pair as usize] };

        // 统一使用event_time对比
        if trade.event_time > update_time {
            // trade 来了之后，为了更高的胜率，假设
            if trade.last_trade_is_buyer_maker {
                // is_buyer_maker 说明是买1成交，应该更新bid价
                unsafe {
                    TRADING_PAIR_RATES[pair as usize][EdgeDirection::Reverse as usize] =
                        trade.last_trade_price * 0.999;
                }
            } else {
                unsafe {
                    TRADING_PAIR_RATES[pair as usize][EdgeDirection::Forward as usize] =
                        1.0 / (trade.last_trade_price * 1.001);
                }
            }
            unsafe {
                TRADING_PAIR_RATE_UPDATE_TIME[pair as usize] = trade.event_time;
            }
        }
    }

    pub fn compute_orders(circle_index: usize) -> Option<usize> {
        let ring = PREDEFINED_RINGS[circle_index];
        for try_count in 0..100 {
            let mut init_amount: f64 = 0.0;
            let mut last_asset_q = 0.0f64;
            let mut i = 0;
            while i < ring.len() {
                let (pair, direction) = ring[i];
                let order_filters = ORDER_FILTERS[pair as usize];
                if i == 0 {
                    if try_count == 0 {
                        let init_q = match direction {
                            EdgeDirection::Forward => match pair.quote() {
                                Currency::XETH => 0.002f64,
                                // Currency::XBTC => 0.0001f64,
                                _ => 10.0f64,
                            },
                            EdgeDirection::Reverse => match pair.base() {
                                Currency::XETH => 0.002f64,
                                // Currency::XBTC => 0.0001f64,
                                _ => 1.0f64,
                            },
                        };
                        match direction {
                            EdgeDirection::Forward => unsafe {
                                let base_asset_q = quote_to_base_quantity(init_q, pair);
                                let base_asset_q_adjusted = adjust_quantity(
                                    base_asset_q,
                                    order_filters[ORDER_FILTER_MIN_ORDER_QTY_INDEX],
                                    order_filters[ORDER_FILTER_LOT_SIZE_INDEX],
                                )?;
                                ORDER_QUANTITIES[i] = base_asset_q_adjusted;
                                init_amount = base_to_quote_quantity(ORDER_QUANTITIES[i], pair);
                                last_asset_q = ORDER_QUANTITIES[i];
                            },
                            EdgeDirection::Reverse => unsafe {
                                let base_asset_q_adjusted = adjust_quantity(
                                    init_q,
                                    order_filters[ORDER_FILTER_MIN_ORDER_QTY_INDEX],
                                    order_filters[ORDER_FILTER_LOT_SIZE_INDEX],
                                )?;
                                last_asset_q = base_to_quote_quantity(base_asset_q_adjusted, pair);
                                ORDER_QUANTITIES[i] = base_asset_q_adjusted;
                                init_amount = base_asset_q_adjusted;
                            },
                        }
                    } else {
                        unsafe {
                            ORDER_QUANTITIES[i] += order_filters[ORDER_FILTER_LOT_SIZE_INDEX];
                            match direction {
                                EdgeDirection::Forward => {
                                    init_amount = base_to_quote_quantity(ORDER_QUANTITIES[i], pair);
                                    last_asset_q = ORDER_QUANTITIES[i];
                                }
                                EdgeDirection::Reverse => {
                                    init_amount = ORDER_QUANTITIES[i];
                                    last_asset_q =
                                        base_to_quote_quantity(ORDER_QUANTITIES[i], pair);
                                }
                            }
                        }
                    }
                    i += 1;
                    continue;
                }
                let q = match direction {
                    EdgeDirection::Forward => quote_to_base_quantity(last_asset_q, pair),
                    EdgeDirection::Reverse => last_asset_q,
                };
                let q = match adjust_quantity(
                    q,
                    order_filters[ORDER_FILTER_MIN_ORDER_QTY_INDEX],
                    order_filters[ORDER_FILTER_LOT_SIZE_INDEX],
                ) {
                    Some(r) => r,
                    None => break,
                };
                unsafe {
                    ORDER_QUANTITIES[i] = q;
                }
                last_asset_q = match direction {
                    EdgeDirection::Forward => q,
                    EdgeDirection::Reverse => base_to_quote_quantity(q, pair),
                };
                i += 1;
            }
            if i < ring.len() {
                continue;
            }

            match ring[ring.len() - 1].1 {
                EdgeDirection::Forward => unsafe {
                    if ((ORDER_QUANTITIES[ring.len() - 1] - init_amount) / init_amount).abs() < 0.1
                    {
                        return Some(ring.len());
                    } else {
                        return None;
                    }
                },
                EdgeDirection::Reverse => {
                    if ((last_asset_q - init_amount) / init_amount).abs() < 0.1 {
                        return Some(ring.len());
                    } else {
                        return None;
                    }
                }
            }
        }
        None
    }

    // #[perf_macro::measure]
    pub fn check_arbitrage<T: Into<TradingPair>>(pair: T) -> Option<usize> {
        let pair = pair.into();
        let ring_indices = TRADING_PAIR_TO_RING_INDEX[pair as usize];
        let mut max_product = 0.0f64;
        let mut result: usize = 0;
        for index in ring_indices {
            let ring = PREDEFINED_RINGS[*index];
            let mut product = 1.0;
            // let mut total_fee = 0.0;

            for &(pair, dir) in ring {
                let rate = unsafe { TRADING_PAIR_RATES[pair as usize][dir as usize] };
                if rate == 0.0 {
                    return None;
                }
                product *= rate;

                // 计算该交易对的实际手续费 (taker fee * BNB 折扣)
                // let taker_fee = unsafe { TRADING_FEES[pair as usize][TAKER_FEE_INDEX] };
                // let actual_fee = taker_fee * BNB_DISCOUNT;
                // total_fee += if taker_fee == 0.0 {
                //     0.0
                // } else {
                //     0.0001725 * 1.1
                // };
            }

            // 检查套利机会：产品必须大于 (1 + 总手续费)
            // let threshold = 1.0 + total_fee;
            // 只套大价差的机会
            if product > 1.001 && product > max_product {
                result = *index;
                max_product = product;
            }
        }
        if max_product > 0.0 {
            for i in 0..PREDEFINED_RINGS[result].len() {
                let (pair, dir) = PREDEFINED_RINGS[result][i];
                let rate = unsafe { TRADING_PAIR_RATES[pair as usize][dir as usize] };
                unsafe {
                    match dir {
                        EdgeDirection::Forward => {
                            EXPECT_PRICE[i] = 1.0 / rate;
                        }
                        EdgeDirection::Reverse => {
                            EXPECT_PRICE[i] = rate;
                        }
                    }
                }
            }
            Some(result)
        } else {
            None
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    fn find_ring(pair1: TradingPair, pair2: TradingPair, pair3: TradingPair) -> Option<usize> {
        for i in 0..PREDEFINED_RINGS.len() {
            let ring = PREDEFINED_RINGS[i];
            if ring.len() >= 3 && ring[0].0 == pair1 && ring[1].0 == pair2 && ring[2].0 == pair3 {
                return Some(i);
            }
        }
        None
    }

    fn setup_test_rates() {
        // 清零所有汇率
        unsafe {
            use std::ptr::addr_of_mut;
            let rates_ptr = addr_of_mut!(TRADING_PAIR_RATES);
            for i in 0..28 {
                // 硬编码长度以避免引用静态变量
                (*rates_ptr)[i][EdgeDirection::Forward as usize] = 0.0;
                (*rates_ptr)[i][EdgeDirection::Reverse as usize] = 0.0;
            }
            // 清零订单数量
            let quantities_ptr = addr_of_mut!(ORDER_QUANTITIES);
            for i in 0..10 {
                // 硬编码长度以避免引用静态变量
                (*quantities_ptr)[i] = 0.0;
            }
        }
    }

    #[test]
    fn test_adjust_quantity_basic() {
        // 测试基本的数量调整功能
        assert_eq!(adjust_quantity(10.0, 5.0, 1.0), Some(10.0));
        assert_eq!(adjust_quantity(10.5, 5.0, 1.0), Some(10.0));
        assert_eq!(adjust_quantity(4.0, 5.0, 1.0), None);
        assert_eq!(adjust_quantity(5.0, 5.0, 1.0), Some(5.0));
    }

    #[test]
    fn test_adjust_quantity_step_size() {
        // 测试步长调整 - 使用浮点数比较来避免精度问题
        let result1 = adjust_quantity(10.25, 5.0, 0.1);
        assert!(result1.is_some());
        assert!((result1.unwrap() - 10.2).abs() < 1e-10);

        let result2 = adjust_quantity(10.05, 5.0, 0.1);
        assert!(result2.is_some());
        assert!((result2.unwrap() - 10.0).abs() < 1e-10);

        assert_eq!(adjust_quantity(4.95, 5.0, 0.1), None);
    }

    #[test]
    fn test_adjust_quantity_edge_cases() {
        // 测试边界情况

        // 1. 零值测试
        assert_eq!(adjust_quantity(0.0, 0.0, 1.0), Some(0.0));
        assert_eq!(adjust_quantity(0.0, 1.0, 1.0), None);

        // 2. 步长为零的情况 - 这可能导致除零错误或无限循环
        // 这是一个潜在的 bug！
        // assert_eq!(adjust_quantity(10.0, 5.0, 0.0), None); // 这会导致除零

        // 3. 负数测试
        assert_eq!(adjust_quantity(-10.0, 5.0, 1.0), None);
        assert_eq!(adjust_quantity(10.0, -5.0, 1.0), Some(10.0)); // 负的最小值？

        // 4. 非常小的步长
        let result = adjust_quantity(1.0, 0.5, 1e-10);
        assert!(result.is_some());

        // 5. 数量刚好等于最小值
        assert_eq!(adjust_quantity(5.0, 5.0, 1.0), Some(5.0));

        // 6. 数量略小于最小值但在一个步长内
        assert_eq!(adjust_quantity(4.9, 5.0, 1.0), None);

        // 7. 非常大的数值
        let large_qty = 1e15;
        let result = adjust_quantity(large_qty, 1e10, 1e5);
        assert!(result.is_some());
    }

    #[test]
    fn test_adjust_quantity_min_multiple_logic() {
        // 测试 min_multiple 逻辑中的潜在 bug

        // 当 qty < min_qty 时，函数会计算 min_multiple
        // 这里测试这个逻辑是否正确

        // 情况1: min_multiple 刚好等于 qty
        let result = adjust_quantity(5.0, 5.1, 0.1);
        // min_multiple = ceil(5.1 / 0.1) * 0.1 = 51 * 0.1 = 5.1
        // 5.1 > 5.0，所以应该返回 None
        assert_eq!(result, None);

        // 情况2: min_multiple 小于 qty
        let result = adjust_quantity(5.2, 5.1, 0.1);
        // 这应该返回 Some，因为 5.2 >= 5.1
        assert!(result.is_some());

        // 情况3: 测试 ceil 计算的精度问题
        let result = adjust_quantity(1.0, 1.01, 0.01);
        // min_multiple = ceil(1.01 / 0.01) * 0.01 = 101 * 0.01 = 1.01
        // 1.01 > 1.0，所以应该返回 None
        assert_eq!(result, None);
    }

    #[test]
    #[should_panic]
    fn test_compute_orders_invalid_ring_index() {
        setup_test_rates();
        // 测试无效的环索引
        let _result = ArbitrageEngine::compute_orders(9999);
        // 这应该会 panic 或返回 None，取决于实现
        // 由于使用了 unsafe 访问，这可能会导致 panic
    }

    #[test]
    fn test_compute_orders_no_rates() {
        setup_test_rates();
        // 测试没有设置汇率的情况
        let result = ArbitrageEngine::compute_orders(0);
        assert_eq!(result, None);
    }
}
