{"cells": [{"cell_type": "code", "execution_count": 13, "id": "d7736279", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "# pd.options.display.datetime_format = \"%Y-%m-%d %H:%M:%S.%f\"\n", "pd.options.display.precision = 6          # 浮点同时保留 6 位小数\n", "pd.set_option('display.max_rows', None)      # 行数不限\n", "pd.set_option('display.max_columns', None)   # 列数不限\n", "\n", "# 宽度相关\n", "pd.set_option('display.width', None)         # 根据终端自动换行\n", "pd.set_option('display.max_colwidth', None)  # 列内容（字符串）长度不限\n", "\n", "# partiusdt= pd.read_parquet(\"../data/data/partiusdt_trades_20250701_121910.parquet\")\n", "# partiusdc = pd.read_parquet(\"../data/data/partiusdc_trades_20250701_121910.parquet\")\n", "# bmtusdt = pd.read_parquet(\"../data/data/bmtusdt_trades_20250701_121910.parquet\")\n", "trade = pd.read_parquet(\"../data/data/partiusdc_bbo_20250703_084651.parquet\")\n", "bbo = pd.read_parquet(\"../data/data/partiusdc_trades_20250703_084651.parquet\")"]}, {"cell_type": "code", "execution_count": 14, "id": "fe1ff0e1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                  timestamp     symbol  bid_price  bid_qty  ask_price  \\\n", "753 2025-07-03 08:45:55.055  PARTIUSDC     0.2077   2184.8     0.2082   \n", "754 2025-07-03 08:45:55.056  PARTIUSDC     0.2077   2184.8     0.2082   \n", "755 2025-07-03 08:45:55.057  PARTIUSDC     0.2077   2980.9     0.2082   \n", "756 2025-07-03 08:45:55.102  PARTIUSDC     0.2077   2184.8     0.2082   \n", "757 2025-07-03 08:45:55.258  PARTIUSDC     0.2077   7379.2     0.2082   \n", "758 2025-07-03 08:45:55.986  PARTIUSDC     0.2077   6879.2     0.2082   \n", "759 2025-07-03 08:45:55.986  PARTIUSDC     0.2076    500.0     0.2082   \n", "760 2025-07-03 08:45:55.986  PARTIUSDC     0.2072    590.7     0.2082   \n", "761 2025-07-03 08:45:55.986  PARTIUSDC     0.2072    111.1     0.2082   \n", "762 2025-07-03 08:45:55.986  PARTIUSDC     0.2072    111.1     0.2077   \n", "763 2025-07-03 08:45:55.987  PARTIUSDC     0.2071    927.8     0.2077   \n", "764 2025-07-03 08:45:55.987  PARTIUSDC     0.2071    880.1     0.2077   \n", "765 2025-07-03 08:45:55.988  PARTIUSDC     0.2069   1338.5     0.2077   \n", "766 2025-07-03 08:45:55.988  PARTIUSDC     0.2067  19722.9     0.2077   \n", "767 2025-07-03 08:45:55.988  PARTIUSDC     0.2067  19722.9     0.2076   \n", "768 2025-07-03 08:45:55.988  PARTIUSDC     0.2067  19399.0     0.2076   \n", "769 2025-07-03 08:45:55.989  PARTIUSDC     0.2066   3200.5     0.2076   \n", "770 2025-07-03 08:45:55.989  PARTIUSDC     0.2069   2404.4     0.2076   \n", "771 2025-07-03 08:45:55.989  PARTIUSDC     0.2073   9964.2     0.2076   \n", "772 2025-07-03 08:45:55.989  PARTIUSDC     0.2073   9964.2     0.2076   \n", "773 2025-07-03 08:45:55.989  PARTIUSDC     0.2073   9964.2     0.2075   \n", "774 2025-07-03 08:45:55.990  PARTIUSDC     0.2069   2404.4     0.2075   \n", "775 2025-07-03 08:45:55.991  PARTIUSDC     0.2069   2404.4     0.2073   \n", "776 2025-07-03 08:45:55.991  PARTIUSDC     0.2071   2404.4     0.2073   \n", "777 2025-07-03 08:45:55.998  PARTIUSDC     0.2071   2404.4     0.2075   \n", "\n", "     ask_qty  update_id  \n", "753   2882.1   45861587  \n", "754   3693.2   45861588  \n", "755   3693.2   45861591  \n", "756   3693.2   45861592  \n", "757   3693.2   45861594  \n", "758   3693.2   45861604  \n", "759   3693.2   45861610  \n", "760   3693.2   45861611  \n", "761   3693.2   45861612  \n", "762   5076.0   45861615  \n", "763   5076.0   45861627  \n", "764   5076.0   45861635  \n", "765   5076.0   45861649  \n", "766   5076.0   45861652  \n", "767    811.1   45861653  \n", "768    811.1   45861655  \n", "769    811.1   45861656  \n", "770    811.1   45861658  \n", "771    811.1   45861660  \n", "772    949.2   45861663  \n", "773    138.1   45861665  \n", "774    138.1   45861669  \n", "775   4000.0   45861676  \n", "776   4000.0   45861678  \n", "777    138.1   45861701  \n", "                 timestamp     symbol  trade_id   price  quantity  \\\n", "16 2025-07-03 08:45:55.984  PARTIUSDC    571385  0.2077     482.3   \n", "17 2025-07-03 08:45:55.984  PARTIUSDC    571386  0.2077    5194.4   \n", "18 2025-07-03 08:45:55.984  PARTIUSDC    571387  0.2076     500.0   \n", "19 2025-07-03 08:45:55.984  PARTIUSDC    571388  0.2075     500.0   \n", "20 2025-07-03 08:45:55.984  PARTIUSDC    571389  0.2074     500.0   \n", "21 2025-07-03 08:45:55.984  PARTIUSDC    571390  0.2074    5097.8   \n", "22 2025-07-03 08:45:55.984  PARTIUSDC    571391  0.2073     479.6   \n", "23 2025-07-03 08:45:55.984  PARTIUSDC    571392  0.2073    1019.5   \n", "24 2025-07-03 08:45:55.985  PARTIUSDC    571393  0.2072     111.1   \n", "25 2025-07-03 08:45:55.985  PARTIUSDC    571394  0.2071    6775.1   \n", "26 2025-07-03 08:45:55.985  PARTIUSDC    571395  0.2071      47.7   \n", "\n", "   buyer_order_id seller_order_id              trade_time  is_buyer_maker  \n", "16           None            None 2025-07-03 08:45:55.984            True  \n", "17           None            None 2025-07-03 08:45:55.984            True  \n", "18           None            None 2025-07-03 08:45:55.984            True  \n", "19           None            None 2025-07-03 08:45:55.984            True  \n", "20           None            None 2025-07-03 08:45:55.984            True  \n", "21           None            None 2025-07-03 08:45:55.984            True  \n", "22           None            None 2025-07-03 08:45:55.984            True  \n", "23           None            None 2025-07-03 08:45:55.984            True  \n", "24           None            None 2025-07-03 08:45:55.985            True  \n", "25           None            None 2025-07-03 08:45:55.985            True  \n", "26           None            None 2025-07-03 08:45:55.985            True  \n"]}], "source": ["# partiusdt = partiusdt[(partiusdt['timestamp'] > \"2025-07-01 20:15:05.000\") & (partiusdt['timestamp'] < \"2025-07-01 20:15:06.000\")]\n", "trade = trade[(trade['timestamp'] > \"2025-07-03 08:45:54.700\") & (trade['timestamp'] < \"2025-07-03 08:45:56.000\")]\n", "print(trade)\n", "bbo = bbo[(bbo['timestamp'] > \"2025-07-03 08:45:54.700\") & (bbo['timestamp'] < \"2025-07-03 08:45:56.000\")]\n", "print(bbo)\n", "# bmtusdt = bmtusdt[(bmtusdt['timestamp'] > \"2025-07-01 12:15:05.000\") & (bmtusdt['timestamp'] < \"2025-07-01 12:17:36.000\")]\n", "# bmtusdc = bmtusdc[(bmtusdc['timestamp'] > \"2025-07-01 12:15:35.000\") & (bmtusdc['timestamp'] < \"2025-07-01 12:17:36.000\")]\n", "\n", "# print(bmtusdt)\n", "# print(bmtusdc)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}