/// 测试depth20和depthupdate SBE解析功能
///
/// 这个测试文件专门用于验证新实现的depth解析器
use libwebsocket_rs::encoding::sbe::{
    decoder::SbeDecoder,
    depth_snapshot::parse_sbe_depth_snapshot,
    depth_update::parse_sbe_depth_update,
    schema::{SbeHeader, SbeMessageType},
};

/// 创建测试用的depth20 SBE消息
fn create_test_depth_snapshot_sbe() -> Vec<u8> {
    let mut data = Vec::new();

    // SBE头部 (8字节)
    data.extend_from_slice(&20u16.to_le_bytes()); // block_length = 20
    data.extend_from_slice(&10002u16.to_le_bytes()); // template_id = 10002 (DepthSnapshot)
    data.extend_from_slice(&1u16.to_le_bytes()); // schema_id = 1
    data.extend_from_slice(&1u16.to_le_bytes()); // version = 1

    // 消息体 (20字节)
    data.extend_from_slice(&1234567890123456u64.to_le_bytes()); // eventTime (8字节)
    data.extend_from_slice(&987654321u64.to_le_bytes()); // lastUpdateId (8字节)
    data.push((-8i8) as u8); // priceExponent (1字节)
    data.push((-8i8) as u8); // qtyExponent (1字节)
    data.push(2u8); // bidsCount (1字节)
    data.push(2u8); // asksCount (1字节)

    // Bid levels (2个)
    data.extend_from_slice(&5000000000000i64.to_le_bytes()); // bid1 price
    data.extend_from_slice(&100000000i64.to_le_bytes()); // bid1 qty
    data.extend_from_slice(&4999900000000i64.to_le_bytes()); // bid2 price
    data.extend_from_slice(&200000000i64.to_le_bytes()); // bid2 qty

    // Ask levels (2个)
    data.extend_from_slice(&5000100000000i64.to_le_bytes()); // ask1 price
    data.extend_from_slice(&150000000i64.to_le_bytes()); // ask1 qty
    data.extend_from_slice(&5000200000000i64.to_le_bytes()); // ask2 price
    data.extend_from_slice(&250000000i64.to_le_bytes()); // ask2 qty

    // symbol: "BTCUSDT"
    data.push(7u8); // 长度
    data.extend_from_slice(b"BTCUSDT");

    data
}

/// 创建测试用的depthupdate SBE消息
fn create_test_depth_update_sbe() -> Vec<u8> {
    let mut data = Vec::new();

    // SBE头部 (8字节)
    data.extend_from_slice(&28u16.to_le_bytes()); // block_length = 28
    data.extend_from_slice(&10003u16.to_le_bytes()); // template_id = 10003 (DepthUpdate)
    data.extend_from_slice(&1u16.to_le_bytes()); // schema_id = 1
    data.extend_from_slice(&1u16.to_le_bytes()); // version = 1

    // 消息体 (28字节)
    data.extend_from_slice(&1234567890123456u64.to_le_bytes()); // eventTime (8字节)
    data.extend_from_slice(&987654320u64.to_le_bytes()); // firstUpdateId (8字节)
    data.extend_from_slice(&987654322u64.to_le_bytes()); // finalUpdateId (8字节)
    data.push((-8i8) as u8); // priceExponent (1字节)
    data.push((-8i8) as u8); // qtyExponent (1字节)
    data.push(1u8); // bidUpdatesCount (1字节)
    data.push(1u8); // askUpdatesCount (1字节)

    // Bid updates (1个)
    data.extend_from_slice(&5000000000000i64.to_le_bytes()); // bid price
    data.extend_from_slice(&0i64.to_le_bytes()); // bid qty (0表示删除)

    // Ask updates (1个)
    data.extend_from_slice(&5000100000000i64.to_le_bytes()); // ask price
    data.extend_from_slice(&300000000i64.to_le_bytes()); // ask qty

    // symbol: "BTCUSDT"
    data.push(7u8); // 长度
    data.extend_from_slice(b"BTCUSDT");

    data
}

#[test]
fn test_depth_snapshot_parsing() {
    let sbe_data = create_test_depth_snapshot_sbe();

    // 测试直接解析
    let depth_snapshot = parse_sbe_depth_snapshot(&sbe_data).unwrap();

    assert_eq!(depth_snapshot.symbol, "BTCUSDT");
    assert_eq!(depth_snapshot.event_time, 1234567890123456);
    assert_eq!(depth_snapshot.last_update_id, 987654321);

    // 验证bid levels
    assert_eq!(depth_snapshot.bids.len(), 2);
    assert_eq!(depth_snapshot.bids[0].price, 50000.0);
    assert_eq!(depth_snapshot.bids[0].qty, 1.0);
    assert_eq!(depth_snapshot.bids[1].price, 49999.0);
    assert_eq!(depth_snapshot.bids[1].qty, 2.0);

    // 验证ask levels
    assert_eq!(depth_snapshot.asks.len(), 2);
    assert_eq!(depth_snapshot.asks[0].price, 50001.0);
    assert_eq!(depth_snapshot.asks[0].qty, 1.5);
    assert_eq!(depth_snapshot.asks[1].price, 50002.0);
    assert_eq!(depth_snapshot.asks[1].qty, 2.5);
}

#[test]
fn test_depth_update_parsing() {
    let sbe_data = create_test_depth_update_sbe();

    // 测试直接解析
    let depth_update = parse_sbe_depth_update(&sbe_data).unwrap();

    assert_eq!(depth_update.symbol, "BTCUSDT");
    assert_eq!(depth_update.event_time, 1234567890123456);
    assert_eq!(depth_update.first_update_id, 987654320);
    assert_eq!(depth_update.final_update_id, 987654322);

    // 验证bid updates
    assert_eq!(depth_update.bid_updates.len(), 1);
    assert_eq!(depth_update.bid_updates[0].price, 50000.0);
    assert_eq!(depth_update.bid_updates[0].qty, 0.0); // 删除操作

    // 验证ask updates
    assert_eq!(depth_update.ask_updates.len(), 1);
    assert_eq!(depth_update.ask_updates[0].price, 50001.0);
    assert_eq!(depth_update.ask_updates[0].qty, 3.0);
}

#[test]
fn test_sbe_decoder_with_depth_messages() {
    // 测试depth snapshot
    let depth_snapshot_data = create_test_depth_snapshot_sbe();

    // 验证SBE检测
    assert!(SbeDecoder::is_sbe_message(&depth_snapshot_data));

    // 验证消息类型
    let msg_type = SbeDecoder::get_message_type(&depth_snapshot_data).unwrap();
    assert_eq!(msg_type, SbeMessageType::DepthSnapshot);

    // 验证解码
    let message = SbeDecoder::decode(&depth_snapshot_data).unwrap();
    match message {
        libwebsocket_rs::encoding::sbe::decoder::SbeMessage::DepthSnapshot(snapshot) => {
            assert_eq!(snapshot.symbol, "BTCUSDT");
            assert_eq!(snapshot.bids.len(), 2);
            assert_eq!(snapshot.asks.len(), 2);
        }
        _ => panic!("Expected DepthSnapshot message"),
    }

    // 测试depth update
    let depth_update_data = create_test_depth_update_sbe();

    // 验证SBE检测
    assert!(SbeDecoder::is_sbe_message(&depth_update_data));

    // 验证消息类型
    let msg_type = SbeDecoder::get_message_type(&depth_update_data).unwrap();
    assert_eq!(msg_type, SbeMessageType::DepthUpdate);

    // 验证解码
    let message = SbeDecoder::decode(&depth_update_data).unwrap();
    match message {
        libwebsocket_rs::encoding::sbe::decoder::SbeMessage::DepthUpdate(update) => {
            assert_eq!(update.symbol, "BTCUSDT");
            assert_eq!(update.bid_updates.len(), 1);
            assert_eq!(update.ask_updates.len(), 1);
        }
        _ => panic!("Expected DepthUpdate message"),
    }
}

#[test]
fn test_batch_depth_parsing() {
    let depth_snapshot_data = create_test_depth_snapshot_sbe();
    let depth_update_data = create_test_depth_update_sbe();

    // 创建批量数据
    let mut batch_data = Vec::new();
    batch_data.extend_from_slice(&depth_snapshot_data);
    batch_data.extend_from_slice(&depth_update_data);

    // 批量解码
    let messages = SbeDecoder::decode_batch(&batch_data);
    assert_eq!(messages.len(), 2);

    // 验证第一个消息 (DepthSnapshot)
    match &messages[0] {
        libwebsocket_rs::encoding::sbe::decoder::SbeMessage::DepthSnapshot(snapshot) => {
            assert_eq!(snapshot.symbol, "BTCUSDT");
        }
        _ => panic!("Expected DepthSnapshot message"),
    }

    // 验证第二个消息 (DepthUpdate)
    match &messages[1] {
        libwebsocket_rs::encoding::sbe::decoder::SbeMessage::DepthUpdate(update) => {
            assert_eq!(update.symbol, "BTCUSDT");
        }
        _ => panic!("Expected DepthUpdate message"),
    }
}

#[test]
fn test_message_validation() {
    let valid_data = create_test_depth_snapshot_sbe();
    assert!(SbeDecoder::validate_message(&valid_data));

    // 测试不完整的消息
    let incomplete_data = &valid_data[..10];
    assert!(!SbeDecoder::validate_message(incomplete_data));

    // 测试无效头部
    let mut invalid_data = valid_data.clone();
    invalid_data[4] = 99; // 修改schema_id为无效值
    assert!(!SbeDecoder::validate_message(&invalid_data));
}
