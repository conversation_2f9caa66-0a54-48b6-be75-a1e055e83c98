/// 简单的depth解析测试
///
/// 这个测试文件专门用于验证depth解析器的基本功能
use libwebsocket_rs::encoding::sbe::{
    depth_snapshot::parse_sbe_depth_snapshot, depth_update::parse_sbe_depth_update,
};

#[test]
fn test_simple_depth_snapshot() {
    // 创建一个简单的测试消息
    let mut data = Vec::new();

    // SBE头部 (8字节)
    data.extend_from_slice(&20u16.to_le_bytes()); // block_length = 20
    data.extend_from_slice(&10002u16.to_le_bytes()); // template_id = 10002
    data.extend_from_slice(&1u16.to_le_bytes()); // schema_id = 1
    data.extend_from_slice(&1u16.to_le_bytes()); // version = 1

    // 消息体 (20字节)
    data.extend_from_slice(&1234567890123456u64.to_le_bytes()); // eventTime (8字节)
    data.extend_from_slice(&987654321u64.to_le_bytes()); // lastUpdateId (8字节)
    data.push((-8i8) as u8); // priceExponent (1字节)
    data.push((-8i8) as u8); // qtyExponent (1字节)
    data.push(1u8); // bidsCount = 1 (1字节)
    data.push(1u8); // asksCount = 1 (1字节)

    // 1个bid level (16字节)
    data.extend_from_slice(&5000000000000i64.to_le_bytes()); // bid price
    data.extend_from_slice(&100000000i64.to_le_bytes()); // bid qty

    // 1个ask level (16字节)
    data.extend_from_slice(&5000100000000i64.to_le_bytes()); // ask price
    data.extend_from_slice(&150000000i64.to_le_bytes()); // ask qty

    // symbol: "BTC"
    data.push(3u8); // 长度
    data.extend_from_slice(b"BTC");

    println!("Test data length: {}", data.len());
    println!("Test data: {:?}", data);

    // 解析
    let result = parse_sbe_depth_snapshot(&data);
    assert!(result.is_some());

    let snapshot = result.unwrap();
    println!("Parsed symbol: '{}'", snapshot.symbol);
    println!("Parsed event_time: {}", snapshot.event_time);
    println!("Parsed last_update_id: {}", snapshot.last_update_id);
    println!("Bids count: {}", snapshot.bids.len());
    println!("Asks count: {}", snapshot.asks.len());

    assert_eq!(snapshot.symbol, "BTC");
    assert_eq!(snapshot.event_time, 1234567890123456);
    assert_eq!(snapshot.last_update_id, 987654321);
    assert_eq!(snapshot.bids.len(), 1);
    assert_eq!(snapshot.asks.len(), 1);
    assert_eq!(snapshot.bids[0].price, 50000.0);
    assert_eq!(snapshot.bids[0].qty, 1.0);
    assert_eq!(snapshot.asks[0].price, 50001.0);
    assert_eq!(snapshot.asks[0].qty, 1.5);
}

#[test]
fn test_simple_depth_update() {
    // 创建一个简单的测试消息
    let mut data = Vec::new();

    // SBE头部 (8字节)
    data.extend_from_slice(&28u16.to_le_bytes()); // block_length = 28
    data.extend_from_slice(&10003u16.to_le_bytes()); // template_id = 10003
    data.extend_from_slice(&1u16.to_le_bytes()); // schema_id = 1
    data.extend_from_slice(&1u16.to_le_bytes()); // version = 1

    // 消息体 (28字节)
    data.extend_from_slice(&1234567890123456u64.to_le_bytes()); // eventTime (8字节)
    data.extend_from_slice(&987654320u64.to_le_bytes()); // firstUpdateId (8字节)
    data.extend_from_slice(&987654322u64.to_le_bytes()); // finalUpdateId (8字节)
    data.push((-8i8) as u8); // priceExponent (1字节)
    data.push((-8i8) as u8); // qtyExponent (1字节)
    data.push(1u8); // bidUpdatesCount = 1 (1字节)
    data.push(1u8); // askUpdatesCount = 1 (1字节)

    // 1个bid update (16字节)
    data.extend_from_slice(&5000000000000i64.to_le_bytes()); // bid price
    data.extend_from_slice(&0i64.to_le_bytes()); // bid qty (0表示删除)

    // 1个ask update (16字节)
    data.extend_from_slice(&5000100000000i64.to_le_bytes()); // ask price
    data.extend_from_slice(&300000000i64.to_le_bytes()); // ask qty

    // symbol: "BTC"
    data.push(3u8); // 长度
    data.extend_from_slice(b"BTC");

    println!("Test data length: {}", data.len());
    println!("Test data: {:?}", data);

    // 解析
    let result = parse_sbe_depth_update(&data);
    assert!(result.is_some());

    let update = result.unwrap();
    println!("Parsed symbol: '{}'", update.symbol);
    println!("Parsed event_time: {}", update.event_time);
    println!("Parsed first_update_id: {}", update.first_update_id);
    println!("Parsed final_update_id: {}", update.final_update_id);
    println!("Bid updates count: {}", update.bid_updates.len());
    println!("Ask updates count: {}", update.ask_updates.len());

    assert_eq!(update.symbol, "BTC");
    assert_eq!(update.event_time, 1234567890123456);
    assert_eq!(update.first_update_id, 987654320);
    assert_eq!(update.final_update_id, 987654322);
    assert_eq!(update.bid_updates.len(), 1);
    assert_eq!(update.ask_updates.len(), 1);
    assert_eq!(update.bid_updates[0].price, 50000.0);
    assert_eq!(update.bid_updates[0].qty, 0.0); // 删除操作
    assert_eq!(update.ask_updates[0].price, 50001.0);
    assert_eq!(update.ask_updates[0].qty, 3.0);
}
